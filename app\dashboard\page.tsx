"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

export default function Dashboard() {
  const router = useRouter();

  const handleClick = () => {
    router.push("/create");
  };

  return (
    <div className="flex flex-1 flex-col gap-4">
      <div className="grid auto-rows-min gap-4 md:grid-cols-3">
        <div className="aspect-video rounded-xl bg-muted/50" />
        <div className="aspect-video rounded-xl bg-muted/50" />
        <div className="aspect-video rounded-xl bg-muted/50" />
      </div>
      <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
        <div className="flex flex-col items-center justify-center h-full p-8">
          <h1 className="text-4xl font-bold mb-8">Dashboard</h1>
          <p className="text-muted-foreground mb-8 text-center max-w-md">
            Welcome to your Adds AI dashboard. Here you can manage your
            websites, view analytics, and create new projects.
          </p>
          <Button className="text-white" onClick={handleClick}>
            Create Website
          </Button>
        </div>
      </div>
    </div>
  );
}
