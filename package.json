{"name": "adds-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.840.0", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^9.5.0", "@floating-ui/react": "^0.27.12", "@heroicons/react": "^2.2.0", "@prisma/client": "^5.0.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-subscript": "^2.22.3", "@tiptap/extension-superscript": "^2.22.3", "@tiptap/extension-task-item": "^2.22.3", "@tiptap/extension-task-list": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-typography": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@types/js-cookie": "^3.0.6", "bcrypt": "^5.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.514.0", "next": "^15.3.3", "openai": "^5.3.0", "prosemirror-view": "^1.40.0", "react": "^18.2.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.4.5", "@types/react": "^18.2.15", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.24", "prisma": "^5.0.0", "sass": "^1.89.2", "tailwindcss": "^3.3.2", "typescript": "^5.1.3"}}