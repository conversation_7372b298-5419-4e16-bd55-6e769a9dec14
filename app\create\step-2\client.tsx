"use client";
import React, { useState, ChangeEvent, FormEvent, useEffect } from "react";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import deleteIcon from "../../assets/images/icons/delete.svg";
import regenrateIcon from "../../assets/images/icons/regenrate.svg";
import arrowRightIcon from "../../assets/images/icons/right-arrow.svg";
import previewIcon from "../../assets/images/icons/eye.svg";
import editIcon from "../../assets/images/icons/edit.svg";
import Image from "next/image";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";

const contentStyles = ["Informative", "Conversational", "Formal", "Casual"];
const tones = ["Professional", "Friendly", "Serious", "Playful"];
const languages = ["English", "Hindi", "Spanish", "French"];
const refreshCycles = ["Daily", "Weekly", "Monthly"];
const categories = ["Health", "Tech", "Education", "Science"];

type Article = {
  id: number;
  title: string;
  metaDescription: string;
  content: string;
  tags: string[];
  featuredImage: string;
  imagePrompt?: string;
};

type Title = {
  id: number;
  title: string;
};

const totalSteps = 4;

export default function Step2() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [siteId, setSiteId] = useState("");
  const [isValidating, setIsValidating] = useState(true);
  const [validationError, setValidationError] = useState("");
  const [step, setStep] = useState(2); // New state for step

  useEffect(() => {
    const site_id = searchParams.get("siteId");
    if (site_id) {
      setSiteId(site_id as string);
    }
  }, [searchParams]);

  useEffect(() => {
    const validateSiteId = async () => {
      if (!siteId) {
        router.push("/create/step-1");
        return;
      }
      try {
        const res = await fetch("/api/site/validate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ siteId }),
        });
        if (!res.ok) {
          const data = await res.json();
          setValidationError(data.error || "Invalid site ID");
          router.push("/create/step-1");
          return;
        }
        setIsValidating(false);
      } catch (error) {
        setValidationError("Failed to validate site");
        router.push("/create/step-1");
      }
    };

    if (siteId) {
      validateSiteId();
    }
  }, [siteId, router]);

  const maxArticles = 100;
  const minArticles = 1;

  const [form, setForm] = useState({
    contentStyle: contentStyles[0],
    tone: tones[0],
    niche: categories[0],
    language: languages[0],
    refreshCycle: refreshCycles[0],
    numberOfArticles: minArticles,
  });
  const [articles, setArticles] = useState<Article[]>([]);
  const [titles, setTitles] = useState<Title[]>([]);
  const [articlesGenerated, setArticlesGenerated] = useState(0);
  const [generatingTitles, setGeneratingTitles] = useState(false);
  const [generatingArticles, setGeneratingArticles] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleGenerateTitles = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setGeneratingTitles(true);
    setError("");
    // validate form
    if (
      !form.contentStyle ||
      !form.tone ||
      !form.niche ||
      !form.language ||
      !form.refreshCycle
    ) {
      setError("Please fill in all fields correctly.");
      setGeneratingTitles(false);
      return;
    }

    if (form.numberOfArticles > maxArticles) {
      setError(`You can generate maximum ${maxArticles} articles only.`);
      setGeneratingTitles(false);
      return;
    }

    if (form.numberOfArticles < minArticles) {
      setError("You have to generate atleast 1 article.");
      setGeneratingTitles(false);
      return;
    }

    try {
      const res = await fetch("/api/articles/generate/title", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...form,
          numberOfArticles: Number(form.numberOfArticles),
        }),
      });
      const data = await res.json();
      setTitles(data.titles || []);
    } catch (err) {
      setError("Failed to generate titles.");
    } finally {
      setGeneratingTitles(false);
      const titlesDiv = document.getElementById("titles");
      const articlesDiv = document.getElementById("articles");
      if (titlesDiv) {
        titlesDiv.style.display = "block";
      }
      if (articlesDiv) {
        articlesDiv.style.display = "none";
      }
    }
  };

  const handleGenerateArticles = async () => {
    // Hide titles div
    const titlesDiv = document.getElementById("titles");
    const articlesDiv = document.getElementById("articles");
    if (titlesDiv) {
      titlesDiv.style.display = "none";
    }
    if (articlesDiv) {
      articlesDiv.style.display = "block";
    }
    setGeneratingArticles(true);
    setArticlesGenerated(0);
    setError("");
    setArticles([]); // <-- Clear previous articles here

    const promises = titles.map(async (title) => {
      try {
        const res = await fetch("/api/articles/generate/single-article", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            ...form,
            title: title.title,
            articleId: title.id,
          }),
        });
        const data = await res.json();
        setArticles((prev) => [...prev, data.article]);
        generateImage(data.article);
      } catch {
        console.error("Error generating article.");
      } finally {
        setArticlesGenerated((prev) => prev + 1);
      }
    });
    await Promise.allSettled(promises);
    setGeneratingArticles(false);
  };

  const handleDelete = (id: number) => {
    setArticles((prev) => prev.filter((a) => a.id !== id));
  };

  const handleDeleteTitle = (id: number) => {
    setTitles((prev) => prev.filter((t) => t.id !== id));
  };

  // Regenerate a single title
  const handleRegenerateTitle = async (id: number) => {
    try {
      const res = await fetch("/api/articles/generate/title", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...form,
          numberOfArticles: 1,
        }),
      });
      const data = await res.json();
      setTitles((prev) =>
        prev.map((t) =>
          t.id === id ? { ...t, title: data.titles[0].title } : t
        )
      );
    } catch (err) {
      setError("Failed to generate titles.");
    }
  };

  const handleSaveAndContinue = async () => {
    if (!siteId) {
      setError("Site ID is missing. Please start from step 1.");
      return;
    }

    setSaving(true);
    setError("");
    try {
      const res = await fetch("/api/articles/save", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ siteId, articles, form }),
      });
      const data = await res.json();
      if (data.success) {
        router.push(`/create/step-3?siteId=${siteId}`);
      } else {
        setError("Failed to save articles.");
      }
    } catch (err) {
      setError("Failed to save articles.");
    } finally {
      setSaving(false);
    }
  };

  async function generateImage(article: Article) {
    try {
      const res = await fetch("/api/articles/generate/image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          imagePrompt: article.imagePrompt,
          title: article.title,
          niche: form.niche,
        }),
      });

      if (!res.ok) {
        console.log("Image generation failed");
        return;
      }

      const data = await res.json();

      if (data.imageUrl) {
        setArticles((prev) =>
          prev.map((a) =>
            a.id === article.id ? { ...a, featuredImage: data.imageUrl } : a
          )
        );
      }
    } catch (err) {
      console.error(`Error generating image for "${article.title}":`, err);
      // Optional: mark article as failed image gen or show retry option
    }
  }

  // Show loading state while validating
  if (isValidating) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Validating site...</p>
        </div>
      </div>
    );
  }

  // Show error if validation failed
  if (validationError) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{validationError}</p>
          <button
            onClick={() => router.push("/create/step-1")}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Return to Step 1
          </button>
        </div>
      </div>
    );
  }

  const percentage = Math.round(((step - 1) / (totalSteps - 1)) * 100);

  return (
    <div className="min-h-screen bg-gray-50 pt-[77px] pb-[85px]">
      <div className="container mx-auto pt-6 px-4">
        {/* Stepper */}
        <div className="bg-white rounded-xl shadow p-6 flex items-center mb-6">
          <div className="flex items-center w-full">
            <div className="flex items-center gap-4 w-auto xl:w-1/4 border-r pr-4">
              {/* Progress Circle - New Addition */}
              <div className="flex justify-center">
                <div style={{ width: 60, height: 60 }}>
                  <CircularProgressbar
                    className="font-semibold text-sm"
                    value={percentage}
                    text={`${percentage}%`}
                    styles={buildStyles({
                      pathColor: "#0092B8",
                      textColor: "#0F172B",
                      trailColor: "#e5e7eb",
                    })}
                  />
                </div>
              </div>
              <div className="flex flex-col hidden xl:flex">
                <h5 className="font-semibold mt-1">
                  Launch Your Site in Minutes
                </h5>
                <div className="text-sm text-gray-500 mt-1">
                  5 more mins to go!
                </div>
              </div>
            </div>
            <div className="flex-1 flex justify-between items-center gap-4 ml-8">
              <div className="completed step flex flex-col flex-1">
                <span>Step 1</span>
                <div>Add Site Details</div>
              </div>
              <div className="active step flex flex-col flex-1">
                <span>Step 2</span>
                <div>Generate Content</div>
              </div>
              <div className="step flex flex-col flex-1">
                <span>Step 3</span>
                <div>Page Setup</div>
              </div>
              <div className="step flex flex-col flex-1">
                <span>Step 4</span>
                <div>Publish your site</div>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleGenerateTitles}>
          <Card className="bg-white mx-auto p-0 gap-0 mb-6">
            <h2 className="text-xl font-semibold border-b border-gray-200 px-6 py-4">
              Generate Content
            </h2>
            <div className="p-6">
              <div className="grid grid-cols-2 xl:grid-cols-12 gap-6 mb-6">
                <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-4">
                  <Label className="gap-1" htmlFor="contentStyle">
                    Content Style
                  </Label>
                  <Select
                    value={form.contentStyle}
                    onValueChange={(value) =>
                      handleChange({
                        target: { name: "contentStyle", value },
                      } as any)
                    }
                    name="contentStyle"
                  >
                    <SelectTrigger id="contentStyle" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent className="w-full bg-white">
                      {contentStyles.map((s) => (
                        <SelectItem
                          className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                          key={s}
                          value={s}
                        >
                          {s}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-4">
                  <Label className="gap-1" htmlFor="tone">
                    Tone
                  </Label>
                  <Select
                    value={form.tone}
                    onValueChange={(value) =>
                      handleChange({ target: { name: "tone", value } } as any)
                    }
                    name="tone"
                  >
                    <SelectTrigger id="tone" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent className="w-full bg-white">
                      {tones.map((t) => (
                        <SelectItem
                          className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                          key={t}
                          value={t}
                        >
                          {t}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-4">
                  <Label className="gap-1" htmlFor="niche">
                    Niche/Category
                  </Label>
                  <Select
                    value={form.niche}
                    onValueChange={(value) =>
                      handleChange({ target: { name: "niche", value } } as any)
                    }
                    name="niche"
                  >
                    <SelectTrigger id="niche" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent className="w-full bg-white">
                      {categories.map((c) => (
                        <SelectItem
                          className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                          key={c}
                          value={c}
                        >
                          {c}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-4">
                  <Label className="gap-1" htmlFor="language">
                    Language
                  </Label>
                  <Select
                    value={form.language}
                    onValueChange={(value) =>
                      handleChange({
                        target: { name: "language", value },
                      } as any)
                    }
                    name="language"
                  >
                    <SelectTrigger id="language" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent className="w-full bg-white">
                      {languages.map((l) => (
                        <SelectItem
                          className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                          key={l}
                          value={l}
                        >
                          {l}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-4">
                  <Label className="gap-1" htmlFor="refreshCycle">
                    Content Refresh Cycle
                  </Label>
                  <Select
                    value={form.refreshCycle}
                    onValueChange={(value) =>
                      handleChange({
                        target: { name: "refreshCycle", value },
                      } as any)
                    }
                    name="refreshCycle"
                  >
                    <SelectTrigger id="refreshCycle" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent className="w-full bg-white">
                      {refreshCycles.map((r) => (
                        <SelectItem
                          className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                          key={r}
                          value={r}
                        >
                          {r}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-4">
                  <Label htmlFor="numberOfArticles" className="block mb-2">
                    Number of Articles
                  </Label>
                  <Input
                    type="number"
                    name="numberOfArticles"
                    id="numberOfArticles"
                    min={1}
                    max={100}
                    value={form.numberOfArticles}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="md:col-span-3 flex justify-end mt-4">
                <Button
                  className="text-white flex items-center gap-2"
                  type="submit"
                  disabled={generatingTitles}
                >
                  {generatingTitles ? "Generating..." : "Generate Titles"}
                </Button>
              </div>

              {error && <div className="text-red-500 mb-4">{error}</div>}
            </div>
          </Card>

          {/* Progress Bar */}
          {generatingArticles && (
            <div className="w-full my-6">
              <div className="text-sm text-gray-600 mb-1">
                Generating {articlesGenerated} of {titles.length} articles...
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-cyan-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${(articlesGenerated / titles.length) * 100}%`,
                  }}
                ></div>
              </div>
            </div>
          )}

          <Card id="articles" className="bg-white mx-auto p-0 gap-0 hidden">
            <h2 className="text-xl font-semibold border-b border-gray-200 px-6 py-4">
              Articles
            </h2>
            <div className="p-6">
              <div className="gap-6 grid grid-cols-1">
                {articles.map((article: Article) => (
                  <div
                    key={article.id}
                    className="bg-gray-50 rounded-lg p-4 shadow flex gap-6 relative"
                  >
                    {article.featuredImage ? (
                      <img
                        src={article.featuredImage}
                        alt={article.title}
                        width={240} // width in px (60 * 4)
                        height={160} // height in px (40 * 4)
                        className="rounded object-cover"
                      />
                    ) : (
                      <div className="w-60 h-40 bg-gray-200 animate-pulse rounded" />
                    )}
                    <div className="flex-1">
                      <div className="flex gap-2 mb-4">
                        {article.tags.map((tag) => (
                          <span
                            key={tag}
                            className="bg-gray-200 text-gray-900 px-3 py-1 rounded-full text-sm"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <h4 className="font-bold text-lg mb-2">
                        {article.title}
                      </h4>
                      <p className="text-gray-700 mb-2 text-sm line-clamp-4">
                        {article.metaDescription}
                      </p>
                      <div className="flex gap-2 mt-auto relative xl:absolute top-0 right-0 xl:top-4 xl:right-4">
                        {/* <Button type='button' variant="outline" className='bg-white border-teal-700 text-teal-700 flex gap-2 px-3 py-1 text-sm font-normal hover:bg-teal-50'><Image src={previewIcon} alt='preview' onClick={() => handlePreview(article.id)}></Image>Preview</Button> */}
                        <Button
                          type="button"
                          variant="outline"
                          className="bg-white border-cyan-600 text-cyan-600 flex gap-2 px-3 py-1 text-sm font-normal hover:bg-cyan-50"
                        >
                          <Image src={editIcon} alt="edit"></Image>Edit
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          className="bg-white border-red-700 text-red-700 flex gap-2 px-3 py-1 text-sm font-normal hover:bg-red-50"
                          onClick={() => handleDelete(article.id)}
                        >
                          <Image src={deleteIcon} alt="delete"></Image>Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          <Card id="titles" className="bg-white mx-auto p-0 gap-0">
            <h2 className="text-xl font-semibold border-b border-gray-200 px-6 py-4">
              Titles
            </h2>
            <div className="p-6">
              <div className="gap-4 grid grid-cols-1">
                {titles.length === 0 && (
                  <div className="text-gray-500">No titles generated yet</div>
                )}
                {titles.map((title: Title) => (
                  <div
                    key={title.id}
                    className="bg-gray-50 rounded-lg px-4 py-2 border flex justify-between items-center"
                  >
                    <h4 className="font-bold text-md">{title.title}</h4>
                    <div className="flex items-center gap-3">
                      <Button
                        className="p-0 w-7 h-7 flex items-center justify-center"
                        variant="destructive"
                        type="button"
                        onClick={() => handleRegenerateTitle(title.id)}
                      >
                        <Image
                          src={regenrateIcon}
                          alt="Regenerate Icon"
                          width={20}
                        />
                      </Button>
                      <Button
                        className="p-0 w-7 h-7 flex items-center justify-cente"
                        variant="destructive"
                        onClick={() => handleDeleteTitle(title.id)}
                      >
                        <Image src={deleteIcon} alt="Delete Icon" width={20} />
                      </Button>
                    </div>
                  </div>
                ))}
                <div className="flex justify-end mt-4">
                  {titles.length > 0 && (
                    <Button
                      type="button"
                      className="text-white flex items-center gap-2"
                      onClick={handleGenerateArticles}
                      disabled={generatingArticles}
                    >
                      {generatingArticles
                        ? "Generating..."
                        : "Continue Generate Articles"}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>

          <div className="action-footer flex justify-end mt-8 fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 py-4">
            <div className="container mx-auto px-4 flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={() => router.push(`/create/step-1?siteId=${siteId}`)}
              >
                Back
              </Button>
              <Button
                className="text-white flex items-center gap-2"
                onClick={handleSaveAndContinue}
                disabled={saving || articles.length === 0}
              >
                {saving ? (
                  "Saving..."
                ) : (
                  <>
                    Next Step
                    <Image src={arrowRightIcon} alt="Upload Icon" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
