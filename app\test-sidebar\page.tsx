"use client";

import React from "react";
import { SidebarLayout } from "@/components/sidebar-layout";
import { But<PERSON> } from "@/components/ui/button";

export default function TestSidebar() {
  const [count, setCount] = React.useState(0);

  return (
    <SidebarLayout
      breadcrumbs={[
        { label: "Test", href: "/test-sidebar" },
        { label: "Sidebar Test" },
      ]}
    >
      <div className="flex flex-1 flex-col gap-4">
        <div className="grid auto-rows-min gap-4 md:grid-cols-2">
          <div className="aspect-video rounded-xl bg-muted/50 flex items-center justify-center">
            <h2 className="text-lg font-semibold">Test Panel 1</h2>
          </div>
          <div className="aspect-video rounded-xl bg-muted/50 flex items-center justify-center">
            <h2 className="text-lg font-semibold">Test Panel 2</h2>
          </div>
        </div>
        <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
          <div className="flex flex-col items-center justify-center h-full p-8">
            <h1 className="text-4xl font-bold mb-8">Sidebar Test Page</h1>
            <p className="text-muted-foreground mb-8 text-center max-w-md">
              This page tests the sidebar functionality. Try clicking the sidebar toggle button
              in the header to collapse/expand the sidebar.
            </p>
            <div className="flex gap-4 items-center">
              <Button onClick={() => setCount(count + 1)}>
                Click me! Count: {count}
              </Button>
              <Button variant="outline" onClick={() => setCount(0)}>
                Reset
              </Button>
            </div>
          </div>
        </div>
      </div>
    </SidebarLayout>
  );
}
